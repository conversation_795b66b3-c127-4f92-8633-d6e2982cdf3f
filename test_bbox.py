#!/usr/bin/env python3
"""
Test script to verify EPS bounding box reading functionality
"""

import re
import os

def get_eps_bbox(file_path):
    """Extract bounding box information from EPS file"""
    try:
        with open(file_path, 'rb') as f:
            # Read first few KB to find BoundingBox
            content = f.read(8192).decode('latin-1', errors='ignore')
            
            # Look for BoundingBox or HiResBoundingBox
            bbox_patterns = [
                r'%%HiResBoundingBox:\s*([0-9.-]+)\s+([0-9.-]+)\s+([0-9.-]+)\s+([0-9.-]+)',
                r'%%BoundingBox:\s*([0-9.-]+)\s+([0-9.-]+)\s+([0-9.-]+)\s+([0-9.-]+)'
            ]
            
            for pattern in bbox_patterns:
                match = re.search(pattern, content)
                if match:
                    x1, y1, x2, y2 = map(float, match.groups())
                    width = x2 - x1
                    height = y2 - y1
                    print(f"Found bounding box: {x1}, {y1}, {x2}, {y2}")
                    print(f"Dimensions: {width:.1f} x {height:.1f} points")
                    return width, height, (x1, y1, x2, y2)
                    
            # If no bounding box found, return default
            print("No bounding box found in file")
            return None, None, None
            
    except Exception as e:
        print(f"Error reading file: {str(e)}")
        return None, None, None

def test_with_sample_eps():
    """Create a sample EPS file for testing"""
    sample_eps = """%!PS-Adobe-3.0 EPSF-3.0
%%BoundingBox: 0 0 612 792
%%HiResBoundingBox: 0.000000 0.000000 612.000000 792.000000
%%Creator: Test
%%Title: Sample EPS
%%CreationDate: 2024-01-01
%%EndComments

% Simple rectangle
newpath
100 100 moveto
500 100 lineto
500 600 lineto
100 600 lineto
closepath
stroke

showpage
%%EOF
"""
    
    # Write sample EPS file
    with open('sample_test.eps', 'w') as f:
        f.write(sample_eps)
    
    print("Testing with sample EPS file:")
    width, height, bbox = get_eps_bbox('sample_test.eps')
    
    if width and height:
        # Calculate output dimensions at different DPIs
        for dpi in [72, 150, 300]:
            output_width = int((width * dpi) / 72)
            output_height = int((height * dpi) / 72)
            print(f"At {dpi} DPI: {output_width} x {output_height} pixels")
    
    # Clean up
    if os.path.exists('sample_test.eps'):
        os.remove('sample_test.eps')

if __name__ == "__main__":
    test_with_sample_eps()
